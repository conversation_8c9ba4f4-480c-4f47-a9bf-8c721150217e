<?xml version='1.0'?>


<SENSOR name="1203">
	<ExternalPower enable='false' voltage='2.8'>
	</ExternalPower>
	
	<OpenShort enable='false'>
		<config voltage="1200" powercurrent="2000" gpiocurrent="1000"/>
		<pin name="DOVDD" min="100" max="1100" index="0"/>
		<pin name="DVDD" min="100" max="1100" index="1"/>
		<pin name="AVDD" min="100" max="1100" index="2"/>
		<pin name="GND" min="100" max="1100" index="5"/>
		<pin name="CLK_P" min="100" max="1100" index="9"/>
		<pin name="CLK_N" min="100" max="1100" index="10"/>
		<pin name="D0P" min="100" max="1100" index="11"/>
		<pin name="D0N" min="100" max="1100" index="12"/>
		<pin name="D1P" min="100" max="1100" index="13"/>
		<pin name="D1N" min="100" max="1100" index="14"/>
		<pin name="D2P" min="100" max="1100" index="15"/>
		<pin name="D2N" min="100" max="1100" index="16"/>
		<pin name="D3P" min="100" max="1100" index="17"/>
		<pin name="D3N" min="100" max="1100" index="18"/>
		<pin name="MCLK" min="100" max="1100" index="36"/>
		<pin name="PWND" min="100" max="1100" index="37"/>
		<pin name="RESET" min="100" max="1100" index="38"/>
		<pin name="SCL" min="100" max="1100" index="39"/>
		<pin name="SDA" min="100" max="1100" index="40"/>
	</OpenShort>
	
	<GLOBAL>
	//
	</GLOBAL>
	
	<STREAM_ON>

	</STREAM_ON>
	
	<STREAM_OFF>

	</STREAM_OFF>
	
	
	
	<MODE_1 type="1203_MIPI_2L_raw10_2960*1666_30fps_27M">
		
		<BAYERMODE type="2"/>
		<IMAGEFORMAT type="5"/>
		<PIXELPATTERN type="0"/>
		<SHARE type="1"/>
		<CODE type="0"/>
		<TIMEOUT value='3000'/>
		<EMBEDDED line="0" />
		<vc type="01" />
		<REGLIST>
		ff  03fe  f1
		ff  03fe  00
		ff  03fe  10
		ff  03fe  00
		ff  0a38  02
		ff  0a38  03
		ff  0a20  07
		ff  061b  03			
		ff  061c  50
		ff  061d  05
		ff  061e  70
		ff  061f  03
		ff  0a21  08
		ff  0a34  40
		ff  0a35  11
		ff  0a36  5e
		ff  0a37  03
		ff  0314  50
		ff  0315  32
		ff  031c  ce
		ff  0219  47
		ff  0342  04
		ff  0343  b0
		ff  0340  06
		ff  0341  d6
		ff  0345  02
		ff  0347  02
		ff  0348  0b
		ff  0349  98
		ff  034a  06
		ff  034b  8a
		ff  0094  0b
		ff  0095  90
		ff  0096  06
		ff  0097  82
		ff  0099  04
		ff  009b  04
		ff  060c  01
		ff  060e  d2
		ff  060f  05
		ff  070c  01
		ff  070e  d2
		ff  070f  05
		ff  0709  40
		ff  0719  40
		ff  0909  07
		ff  0902  04
		ff  0904  0b
		ff  0907  54
		ff  0908  06
		ff  0903  9d
		ff  072a  1c
		ff  072b  1c
		ff  0724  2b
		ff  0727  2b
		ff  1466  18
		ff  1467  15
		ff  1468  15
		ff  1469  70
		ff  146a  e8
		ff  0707  07
		ff  0737  0f
		ff  0704  01
		ff  0706  02
		ff  0716  02
		ff  0708  c8
		ff  0718  c8 
		ff  061a  02
		ff  1430  80
		ff  1407  10
		ff  1408  16
		ff  1409  03
		ff  1438  01
		ff  02ce  03
		ff  0245  c9
		ff  023a  08
		ff  02cd  88
		ff  0612  02
		ff  0613  c7
		ff  0243  03
		ff  0089  03
		ff  0002  ab			
		ff  0040  a3
		ff  0075  64
		ff  0004  0f
		ff  0053  0a
		ff  0205  0c
		

		ff  0a67  80
		ff  0a54  0e
		ff  0a65  10
		ff  0a98  04
		ff  05be  00
		ff  05a9  01
		ff  0023  00
		ff  0022  00
		ff  0025  00
		ff  0024  00
		ff  0028  0b
		ff  0029  98
		ff  002a  06
		ff  002b  86
		ff  0a83  e0
		ff  0a72  02
		ff  0a73  60
		ff  0a75  41
		ff  0a70  03
		ff  0a5a  80
		
		ff  0181  30
		ff  0182  05
		ff  0185  01
		ff  0180  46
		ff  0100  08
		ff  010d  74
		ff  010e  0e
		ff  0113  02
		ff  0114  01
		ff  0115  10
		ff  0100  09

		ff  0a70  00
		ff  0080  02
		ff  0a67  00
		
		//ff  0089  0a
		//ff  0721  0a
		
		//OB
		ff  0052  02
		ff  0076  01
		ff  021a  10
		ff  0049  0f //darkrow select
		ff  004a  3c
		ff  004b  00
		ff  0430  25
		ff  0431  25
		ff  0432  25
		ff  0433  25
		ff  0434  59
		ff  0435  59
		ff  0436  59
		ff  0437  59

		</REGLIST>
		<!-- Extra I2C initializer : set "front" to "1", if apply extra regs first-->
		<EXREGLIST clock="400" slaveid="0x20" addr_bit="16" val_bit="8" front='0'>
	

		</EXREGLIST>
	</MODE_1>	

	<MODE_2 type="1203_MIPI_2L_raw10_2960*1666_40fps_27M">
		
		<BAYERMODE type="2"/>
		<IMAGEFORMAT type="5"/>
		<PIXELPATTERN type="0"/>
		<SHARE type="1"/>
		<CODE type="0"/>
		<TIMEOUT value='3000'/>
		<EMBEDDED line="0" />
		<vc type="01" />
		<REGLIST>
ff  03fe  f0
ff  03fe  00
ff  03fe  10
ff  03fe  00
ff  0a38  02
ff  0a38  03
ff  0a20  07

ff  061b  03
ff  061c  50
ff  061d  05
ff  061e  70
ff  061f  03
ff  0a21  08
ff  0a34  40
ff  0a35  11
ff  0a36  80
ff  0a37  03
ff  0314  50
ff  0315  32
ff  031c  ce
ff  0219  47
ff  0342  03
ff  0343  84
ff  0340  06
ff  0341  d6
ff  0345  02
ff  0347  02
ff  0348  0b
ff  0349  98
ff  034a  06
ff  034b  8a
ff  0094  0b
ff  0095  90
ff  0096  06
ff  0097  82
ff  0099  04
ff  009b  04
ff  060c  01
ff  060e  d2
ff  060f  05
ff  070c  01
ff  070e  d2
ff  070f  05
ff  0709  40
ff  0719  40
ff  0909  07
ff  0902  04
ff  0904  0b
ff  0907  54
ff  0908  06
ff  0903  9d
ff  072a  1c
ff  072b  1c
ff  0724  2b
ff  0727  2b
//ff  021b  69
//ff  0222  41
ff  1412  20
ff  1466  18
ff  1467  08
ff  1468  10
ff  1469  80
ff  146a  e8
ff  0707  07
ff  0737  0f
ff  0704  01
ff  0706  02
ff  0716  02
ff  0708  c8
ff  0718  c8 
ff  061a  02
//ff  1430  80
ff  1407  10
ff  1408  16
ff  1409  03
ff  1438  01
ff  02ce  03
ff  0245  c9
ff  023a  08
ff  02cd  88
ff  0612  02
ff  0613  c7
ff  0243  03
ff  0089  03
ff  0002  ab			
ff  0040  a3
ff  0075  64
ff  0004  0f
ff  0053  0a
ff  0205  0c

//auto_load
ff  0a67  80
ff  0a54  0e
ff  0a65  10
ff  0a98  04
ff  05be  00
ff  05a9  01
ff  0028  0b
ff  0029  98
ff  002a  06
ff  002b  86
ff  0a83  e0
ff  0a72  02
ff  0a73  60
ff  0a75  41
ff  0a70  03
ff  0a5a  80

ff  0123  30
ff  0124  04
ff  0125  30
ff  0129  0c
ff  012a  18
ff  012b  18
ff  0181  30
ff  0182  05
ff  0185  01
ff  0180  46
ff  0107  09
ff  0100  08
ff  010d  74
ff  010e  0e
ff  0113  02
ff  0114  01
ff  0115  10
ff  0100  09
//sleep 20
ff  0a70  00
ff  0080  02
ff  0a67  00


//OB
ff  0052  02
ff  0076  01
ff  021a  10
ff  0049  0f //darkrow select
ff  004a  3c
ff  004b  00
ff  0430  25
ff  0431  25
ff  0432  25
ff  0433  25
ff  0434  59
ff  0435  59
ff  0436  59
ff  0437  59

		</REGLIST>
		<!-- Extra I2C initializer : set "front" to "1", if apply extra regs first-->
		<EXREGLIST clock="400" slaveid="0x20" addr_bit="16" val_bit="8" front='0'>
	

		</EXREGLIST>
	</MODE_2>
	
	<MODE_3 type="1203_MIPI_4L_raw10_2960*1666_40fps_27M">
		
		<BAYERMODE type="2"/>
		<IMAGEFORMAT type="5"/>
		<PIXELPATTERN type="0"/>
		<SHARE type="1"/>
		<CODE type="0"/>
		<TIMEOUT value='3000'/>
		<EMBEDDED line="0" />
		<vc type="01" />
		<REGLIST>
ff  03fe  f0
ff  03fe  00
ff  03fe  10
ff  03fe  00
ff  0a38  02
ff  0a38  03
ff  0a20  07
ff  0a28  01
ff  061b  03
ff  061c  50
ff  061d  05
ff  061e  70
ff  061f  03
ff  0a21  08
ff  0a34  40
ff  0a35  11
ff  0a36  80
ff  0a37  03
ff  0314  50
ff  0315  32
ff  031c  ce
ff  0219  47
ff  0342  03
ff  0343  84
ff  0340  06
ff  0341  d6
ff  0345  02
ff  0347  02
ff  0348  0b
ff  0349  98
ff  034a  06
ff  034b  8a
ff  0094  0b
ff  0095  90
ff  0096  06
ff  0097  82
ff  0099  04
ff  009b  04
ff  060c  01
ff  060e  d2
ff  060f  05
ff  070c  01
ff  070e  d2
ff  070f  05
ff  0709  40
ff  0719  40
ff  0909  07
ff  0902  04
ff  0904  0b
ff  0907  54
ff  0908  06
ff  0903  9d
ff  072a  1c
ff  072b  1c
ff  0724  2b
ff  0727  2b
//ff  021b  69
//ff  0222  41
ff  1412  20
ff  1466  18
ff  1467  08
ff  1468  10
ff  1469  80
ff  146a  e8
ff  0707  07
ff  0737  0f
ff  0704  01
ff  0706  02
ff  0716  02
ff  0708  c8
ff  0718  c8 
ff  061a  02
//ff  1430  80
ff  1407  10
ff  1408  16
ff  1409  03
ff  1438  01
ff  02ce  03
ff  0245  c9
ff  023a  08
ff  02cd  88
ff  0612  02
ff  0613  c7
ff  0243  03
ff  0089  03
ff  0002  ab			
ff  0040  a3
ff  0075  64
ff  0004  0f
ff  0053  0a
ff  0205  0c

//auto_load
ff  0a67  80
ff  0a54  0e
ff  0a65  10
ff  0a98  04
ff  05be  00
ff  05a9  01
ff  0028  0b
ff  0029  98
ff  002a  06
ff  002b  86
ff  0a83  e0
ff  0a72  02
ff  0a73  60
ff  0a75  41
ff  0a70  03
ff  0a5a  80

ff  0181  f0//30
ff  0182  05
ff  0185  01
ff  0180  46
ff  0107  09
ff  0100  08
ff  010d  74
ff  010e  0e
ff  0113  02
ff  0114  03//01
ff  0115  10
ff  0100  09
//sleep 20
ff  0a70  00
ff  0080  02
ff  0a67  00


//OB
ff  0052  02
ff  0076  01
ff  021a  10
ff  0049  0f //darkrow select
ff  004a  3c
ff  004b  00
ff  0430  25
ff  0431  25
ff  0432  25
ff  0433  25
ff  0434  59
ff  0435  59
ff  0436  59
ff  0437  59

		</REGLIST>
		<!-- Extra I2C initializer : set "front" to "1", if apply extra regs first-->
		<EXREGLIST clock="400" slaveid="0x20" addr_bit="16" val_bit="8" front='0'>
	

		</EXREGLIST>
	</MODE_3>

	</SENSOR>