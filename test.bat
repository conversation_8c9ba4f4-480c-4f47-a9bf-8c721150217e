
:: ==============================================
:: XML转C文件工具 (模式选择版) - 修复版
:: 修复问题：
:: 1. 修正XPath查询语法错误
:: 2. 优化中文显示处理
:: 3. 增强错误处理机制
:: ==============================================

@echo off

:: ==============================================
:: 强制设置UTF-8编码环境
:: 以下命令必须放在脚本最开头，且文件必须保存为UTF-8 with BOM
:: ==============================================
chcp 65001 >nul
>nul reg add "HKCU\Console" /v "CodePage" /t REG_DWORD /d 65001 /f
setlocal enabledelayedexpansion

:: 初始化日志文件（添加到脚本开头，setlocal之后）
set "log_file=script_operation.log"
echo [%date% %time%] 脚本启动 > "%log_file%"

:: 确保控制台使用支持中文的字体
>nul reg add "HKCU\Console" /v "FaceName" /t REG_SZ /d "Microsoft YaHei" /f
cls

:: 检查PowerShell可用性
where powershell >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 需要PowerShell支持
    pause
    exit /b 1
)

:: =============== 主程序开始 ===============
:: 模式选择菜单
:select_type
cls
echo.
:: 模式选择1=2L，2=4L
echo 请选择模式：
echo 1. 2L
echo 2. 4L
set /p "TYPE_NUM=请输入1或2："
if "!TYPE_NUM!"=="1" (
    set "L_TYPE=2L"
) else if "!TYPE_NUM!"=="2" (
    set "L_TYPE=4L"
) else (
    echo [错误] 请输入1或2进行选择！
    goto select_type
)

:: 帧率选择1=30fps，2=40fps
:select_fps
echo.
echo 请选择帧率：
echo 1. 30fps
echo 2. 40fps
set /p "FPS_NUM=请输入1或2："
if "!FPS_NUM!"=="1" (
    set "FPS=30fps"
) else if "!FPS_NUM!"=="2" (
    set "FPS=40fps"
) else (
    echo [错误] 请输入1或2选择帧率！
    goto select_fps
)

:: 显示用户选择
echo.
echo 您选择的参数：
echo 模式：!L_TYPE!
echo 帧率：!FPS!
echo.

:: 提示用户输入 XML 文件路径
:input_xml
set /p "xml_file=请输入XML文件路径: "
set "xml_file=!xml_file:"=!"
if not exist "!xml_file!" (
    echo [错误] 指定的XML文件不存在，请重新输入！
    goto input_xml
)
echo 已确认XML文件存在：!xml_file!


:: 提示用户输入 C 文件路径
:input_c
set /p "c_file=请输入C文件路径: "
set "c_file=!c_file:"=!"
if not exist "!c_file!" (
    echo [错误] 指定的C文件不存在，请重新输入！
    goto input_c
)
echo 已确认C文件存在：!c_file!


:: 提示输入输出文件路径（自动创建目录）
:: 提示用户输入输出文件路径
:input_output_file
set /p "final_output=请输入输出文件夹路径（不能和原文件相同）: "
set "final_output=!final_output:"=!"

:: 检查是否为空
if "!final_output!"=="" (
    echo [错误] 输出路径不能为空
    goto input_output_file
)

:: 补斜杠（如果没有）
if not "!final_output:~-1!"=="\" (
    set "final_output=!final_output!\"
)

:: 获取原C文件名和扩展名
for %%F in ("!c_file!") do (
    set "c_base=%%~nF"
    set "c_ext=%%~xF"
)

:: 构造最终文件名：如 sensor_config_1.c
set "final_output_file=!final_output!!c_base!_1!c_ext!"

echo 最终输出文件路径: !final_output_file!
>> "%log_file%" echo [INFO] 最终输出文件路径: !final_output_file!

echo ----------------------------------------


:: 使用PowerShell查找同时包含这两个参数的MODE节点

set "ps_command=$xml=[xml](Get-Content '!xml_file!'); $mode=$xml.SelectSingleNode('//*[contains(@type,\"!L_TYPE!\") and contains(@type,\"!FPS!\")]'); if($mode) { $mode.GetAttribute('type') } else { 'NOT_FOUND' }"

for /f "delims=" %%a in ('powershell -Command "!ps_command!"') do (
    set "matched_mode=%%a"
)

if "!matched_mode!"=="NOT_FOUND" (
    echo [错误] 未找到同时包含!L_TYPE!和!FPS!的模式
    pause
    exit /b 1
)
echo 找到匹配模式: !matched_mode!


:: 提取寄存器数据到临时文件
set "reg_tempfile=%temp%\reg_temp_%random%.txt"
>> "%log_file%" echo 寄存器临时文件: !reg_tempfile!
set "ps_extract=$xml=[xml](Get-Content '!xml_file!'); $node=$xml.SelectSingleNode('//*[@type=\"!matched_mode!\"]/REGLIST'); if($node -ne $null) { $node.InnerText } else { '' }"
powershell -Command "!ps_extract!" > "!reg_tempfile!"

:: 验证结果
if not exist "!reg_tempfile!" (
    echo 错误：未能生成寄存器文件
    exit /b 1
)

:: ========== 寄存器转换部分 ==========
set "converted_file=%temp%\converted_reg_%random%.txt"
>> "%log_file%" echo 转换后寄存器文件: !converted_file!

(
    echo // 自动转换的寄存器配置
    echo // 源文件: !xml_file!
    echo // 模式: !matched_mode!
    echo // 转换时间: %date% %time%
    echo.
) > "!converted_file!"

set "counter=0"
for /f "tokens=1-3" %%a in ('type "!reg_tempfile!"') do (
    set "addr=%%b"
    set "value=%%c"
    
    :: 去除所有空格和制表符
    set "addr=!addr: =!"
    set "addr=!addr:    =!"
    set "value=!value: =!"
    set "value=!value:    =!"
    
    :: 验证十六进制格式并确保长度正确
    if "!addr!" neq "" if "!value!" neq "" (
        echo !addr!| findstr /r "^[0-9a-fA-F][0-9a-fA-F]*$" >nul
        if !errorlevel! equ 0 (
            echo !value!| findstr /r "^[0-9a-fA-F][0-9a-fA-F]*$" >nul
            if !errorlevel! equ 0 (
                :: 确保地址和值都是4位十六进制
                set "padded_addr=0000!addr!"
                set "padded_addr=!padded_addr:~-4!"
                set "padded_value=00!value!"
                set "padded_value=!padded_value:~-2!"
                :: 写入C数组格式（与Sensor_init_table格式一致）
                echo 	{0x!padded_addr!,0x!padded_value!}, >> "!converted_file!"
                set /a "counter+=1"
            )
        )
    )
)
echo. >> "!converted_file!"
echo // 共转换 !counter! 个寄存器 >> "!converted_file!"
>> "%log_file%" echo 已转换 !counter! 个寄存器

:: 创建输出目录
for %%F in ("!final_output_file!") do (
    set "output_dir=%%~dpF"
    if not exist "!output_dir!" mkdir "!output_dir!"
)

:: 创建PowerShell脚本文件
set "ps_script=%temp%\replace_script_%random%.ps1"
(
echo $cFile = '%c_file:\=\\%'
echo $regFile = '%converted_file:\=\\%'
echo $outputFile = '%final_output_file:\=\\%'
echo.
echo $content = Get-Content $cFile -Encoding UTF8
echo $regData = Get-Content $regFile -Encoding UTF8
echo.
echo $output = @^(^)
echo $inTable = $false
echo $foundBrace = $false
echo.
echo foreach ^($line in $content^) {
echo     if ^($line -match 'const static I2C_ARRAY Sensor_init_table'^) {
echo         $output += $line
echo         $inTable = $true
echo         $foundBrace = $false
echo     }
echo     elseif ^($inTable -and $line -match '^\s*\{\s*$' -and -not $foundBrace^) {
echo         $output += $line
echo         foreach ^($regLine in $regData^) {
echo             if ^($regLine -match '^\t\{0x'^) {
echo                 $output += $regLine
echo             }
echo         }
echo         $foundBrace = $true
echo     }
echo     elseif ^($inTable -and $line -match '^\s*\};\s*$'^) {
echo         $output += $line
echo         $inTable = $false
echo         $foundBrace = $false
echo     }
echo     elseif ^(-not $inTable -or -not $foundBrace^) {
echo         $output += $line
echo     }
echo }
echo.
echo $output ^| Out-File $outputFile -Encoding UTF8
) > "!ps_script!"

:: 执行PowerShell脚本
powershell -ExecutionPolicy Bypass -File "!ps_script!"

:: 清理PowerShell脚本
del "!ps_script!" 2>nul

echo.
echo ✅ 已成功生成新C文件：!final_output_file!
>> "%log_file%" echo [完成] 替换Sensor_init_table成功，输出文件: !final_output_file!

:: 清理临时文件
if exist "!reg_tempfile!" del "!reg_tempfile!"
if exist "!converted_file!" del "!converted_file!"

pause
